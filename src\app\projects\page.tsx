"use client";

import React, { useState } from 'react';
import { motion } from 'motion/react';
import { useInView } from 'react-intersection-observer';
import Header from '@/components/header/Header';
import Footer from '@/components/footer/Footer';
import ProjectCarousel from '@/components/projects/ProjectCarousel';
import { Filter, ExternalLink, Calendar, MapPin, Building, Users } from 'lucide-react';

const ProjectsPage = () => {
  const [heroRef, heroInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [projectsRef, projectsInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [selectedFilter, setSelectedFilter] = useState('All');

  const filters = ['All', 'Electrical', 'HVAC', 'Fire Safety', 'Plumbing', 'Solar', 'IBMS'];

  const projects = [
    {
      id: 1,
      title: "Corporate Office Complex - Phase 1",
      category: "Electrical",
      status: "Completed",
      location: "Chennai, Tamil Nadu",
      client: "Tech Solutions Pvt Ltd",
      duration: "8 months",
      teamSize: "12 engineers",
      description: "Complete electrical design and installation for a 15-story corporate office building including power distribution, lighting systems, and emergency backup.",
      services: ["Power Distribution", "Lighting Design", "Emergency Systems", "UPS Installation"],
      highlights: [
        "Energy-efficient LED lighting throughout",
        "Smart building automation integration",
        "99.9% power reliability achieved",
        "30% reduction in energy consumption"
      ],
      images: ["/placeholder-project-1.jpg", "/placeholder-project-2.jpg", "/placeholder-project-3.jpg"]
    },
    {
      id: 2,
      title: "Manufacturing Plant HVAC Upgrade",
      category: "HVAC",
      status: "Ongoing",
      location: "Coimbatore, Tamil Nadu",
      client: "Industrial Manufacturing Co",
      duration: "6 months",
      teamSize: "8 engineers",
      description: "Comprehensive HVAC system upgrade for a large manufacturing facility to improve air quality and energy efficiency.",
      services: ["HVAC Design", "Air Quality Control", "Energy Management", "Ventilation Systems"],
      highlights: [
        "40% improvement in air quality",
        "Advanced filtration systems",
        "Temperature control optimization",
        "Reduced operational costs by 25%"
      ],
      images: ["/placeholder-project-4.jpg", "/placeholder-project-5.jpg", "/placeholder-project-6.jpg"]
    },
    {
      id: 3,
      title: "Hospital Fire Safety Systems",
      category: "Fire Safety",
      status: "Completed",
      location: "Bangalore, Karnataka",
      client: "Metro General Hospital",
      duration: "4 months",
      teamSize: "6 engineers",
      description: "Installation of comprehensive fire detection and suppression systems for a 200-bed hospital facility.",
      services: ["Fire Detection", "Sprinkler Systems", "Smoke Management", "Emergency Evacuation"],
      highlights: [
        "Advanced smoke detection technology",
        "Integrated alarm systems",
        "Compliance with healthcare standards",
        "24/7 monitoring capabilities"
      ],
      images: ["/placeholder-project-7.jpg", "/placeholder-project-8.jpg", "/placeholder-project-9.jpg"]
    },
    {
      id: 4,
      title: "Residential Complex Plumbing",
      category: "Plumbing",
      status: "Completed",
      location: "Hyderabad, Telangana",
      client: "Green Valley Developers",
      duration: "10 months",
      teamSize: "15 engineers",
      description: "Complete plumbing infrastructure for a 500-unit residential complex including water supply, drainage, and rainwater harvesting.",
      services: ["Water Supply", "Drainage Systems", "Rainwater Harvesting", "Hot Water Systems"],
      highlights: [
        "Sustainable water management",
        "Rainwater harvesting system",
        "High-efficiency fixtures",
        "Zero water wastage design"
      ],
      images: ["/placeholder-project-10.jpg", "/placeholder-project-11.jpg", "/placeholder-project-12.jpg"]
    },
    {
      id: 5,
      title: "Solar Power Installation - Commercial",
      category: "Solar",
      status: "Ongoing",
      location: "Pune, Maharashtra",
      client: "EcoTech Industries",
      duration: "3 months",
      teamSize: "10 engineers",
      description: "Large-scale solar power installation for commercial facility with grid-tie capabilities and battery storage.",
      services: ["Solar Panel Installation", "Grid Integration", "Battery Storage", "Monitoring Systems"],
      highlights: [
        "500kW solar capacity",
        "Grid-tie with net metering",
        "Advanced monitoring system",
        "ROI within 4 years"
      ],
      images: ["/placeholder-project-13.jpg", "/placeholder-project-14.jpg", "/placeholder-project-15.jpg"]
    },
    {
      id: 6,
      title: "Smart Building Management System",
      category: "IBMS",
      status: "Completed",
      location: "Mumbai, Maharashtra",
      client: "Future Tech Campus",
      duration: "5 months",
      teamSize: "8 engineers",
      description: "Implementation of intelligent building management system for a modern office campus with integrated automation.",
      services: ["Building Automation", "Energy Management", "Security Integration", "Environmental Monitoring"],
      highlights: [
        "Centralized control system",
        "Real-time monitoring",
        "Predictive maintenance",
        "35% energy savings achieved"
      ],
      images: ["/placeholder-project-16.jpg", "/placeholder-project-17.jpg", "/placeholder-project-18.jpg"]
    }
  ];

  const filteredProjects = selectedFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category === selectedFilter);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section ref={heroRef} className="relative py-20 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900">
              Our <span className="text-blue-600">Projects</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Explore our portfolio of successful electrical and MEP engineering projects across various industries and sectors.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap items-center justify-center gap-4">
            <div className="flex items-center space-x-2 text-gray-600">
              <Filter className="w-5 h-5" />
              <span className="font-medium">Filter by:</span>
            </div>
            {filters.map((filter) => (
              <motion.button
                key={filter}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setSelectedFilter(filter)}
                className={`px-4 py-2 rounded-full font-medium transition-colors ${
                  selectedFilter === filter
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filter}
              </motion.button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section ref={projectsRef} className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 50 }}
                animate={projectsInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                {/* Project Images Carousel */}
                <div className="relative h-64 bg-gray-200">
                  <ProjectCarousel images={project.images} />
                  <div className="absolute top-4 left-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      project.status === 'Completed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {project.status}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="px-3 py-1 bg-white/90 text-gray-800 rounded-full text-sm font-medium">
                      {project.category}
                    </span>
                  </div>
                </div>

                {/* Project Details */}
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">{project.title}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{project.description}</p>

                  {/* Project Info */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span>{project.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Building className="w-4 h-4" />
                      <span>{project.client}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Calendar className="w-4 h-4" />
                      <span>{project.duration}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Users className="w-4 h-4" />
                      <span>{project.teamSize}</span>
                    </div>
                  </div>

                  {/* Services */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Services Provided</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.services.map((service, idx) => (
                        <span key={idx} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                          {service}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Key Highlights */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Key Highlights</h4>
                    <ul className="space-y-2">
                      {project.highlights.map((highlight, idx) => (
                        <li key={idx} className="flex items-center space-x-2 text-sm text-gray-600">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span>{highlight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    View Project Details
                    <ExternalLink className="ml-2 w-4 h-4" />
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-600 to-purple-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={projectsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-white">
              Have a Project in Mind?
            </h2>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Let's discuss how we can bring your vision to life with our expertise and proven track record.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors"
            >
              Start Your Project
              <ExternalLink className="ml-2 w-5 h-5" />
            </motion.button>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ProjectsPage;
