import React from 'react';
import { Metadata } from 'next';
import ServicesContent from './ServicesContent';

export const metadata: Metadata = {
  title: 'Our Services - JS Consultants | Electrical, HVAC, Fire Safety & More',
  description: 'Comprehensive electrical and MEP engineering services including HVAC, fire safety, plumbing, solar power, and IBMS solutions. Expert engineering consultancy in Chennai.',
  keywords: 'electrical engineering services, HVAC systems, fire safety, plumbing, solar power, IBMS, MEP engineering, Chennai',
  openGraph: {
    title: 'Professional Engineering Services - JS Consultants',
    description: 'Expert electrical and MEP engineering services for residential, commercial, and industrial projects.',
    type: 'website',
    locale: 'en_IN',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Engineering Services - JS Consultants',
    description: 'Comprehensive electrical and MEP engineering solutions for all your project needs.',
  }
};

export default function ServicesPage() {
  return <ServicesContent />;
}

const ServicesPage = () => {
  const [heroRef, heroInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [servicesRef, servicesInView] = useInView({ threshold: 0.3, triggerOnce: true });
  const [expandedService, setExpandedService] = useState<number | null>(null);

  const services = [
    {
      id: 1,
      title: "Electrical Engineering",
      icon: Zap,
      color: "from-blue-500 to-blue-600",
      description: "Complete electrical design and installation services for residential, commercial, and industrial projects.",
      features: [
        "Power Distribution Systems",
        "Lighting Design & Control",
        "Emergency Power Systems",
        "Electrical Safety & Compliance",
        "Energy Management Systems",
        "Motor Control Centers"
      ],
      capabilities: [
        "Load calculations and power system analysis",
        "Electrical panel design and specification",
        "Cable routing and conduit systems",
        "Grounding and bonding systems",
        "Electrical code compliance and permits",
        "Testing and commissioning"
      ],
      applications: ["Commercial Buildings", "Industrial Facilities", "Residential Complexes", "Healthcare Facilities", "Educational Institutions"]
    },
    {
      id: 2,
      title: "HVAC Systems",
      icon: Wind,
      color: "from-green-500 to-green-600",
      description: "Advanced heating, ventilation, and air conditioning solutions for optimal comfort and efficiency.",
      features: [
        "Climate Control Systems",
        "Air Quality Management",
        "Energy Efficient Design",
        "Ventilation Systems",
        "Ductwork Design",
        "Smart Controls"
      ],
      capabilities: [
        "Load calculations and system sizing",
        "Equipment selection and specification",
        "Ductwork design and layout",
        "Control system integration",
        "Energy modeling and optimization",
        "Maintenance planning"
      ],
      applications: ["Office Buildings", "Retail Spaces", "Manufacturing Plants", "Hospitals", "Data Centers"]
    },
    {
      id: 3,
      title: "Fire Fighting Systems",
      icon: Shield,
      color: "from-red-500 to-red-600",
      description: "Comprehensive fire safety and protection systems to safeguard your property and personnel.",
      features: [
        "Fire Detection Systems",
        "Sprinkler Systems",
        "Fire Suppression",
        "Emergency Evacuation",
        "Smoke Management",
        "Fire Alarm Systems"
      ],
      capabilities: [
        "Fire risk assessment and analysis",
        "System design and hydraulic calculations",
        "Code compliance and approvals",
        "Installation supervision",
        "Testing and commissioning",
        "Maintenance programs"
      ],
      applications: ["High-rise Buildings", "Industrial Facilities", "Warehouses", "Shopping Centers", "Hotels"]
    },
    {
      id: 4,
      title: "Plumbing",
      icon: Droplets,
      color: "from-cyan-500 to-cyan-600",
      description: "Professional plumbing services including design, installation, and maintenance solutions.",
      features: [
        "Water Supply Systems",
        "Drainage & Sewerage",
        "Hot Water Systems",
        "Rainwater Harvesting",
        "Water Treatment",
        "Fixture Installation"
      ],
      capabilities: [
        "Hydraulic design and calculations",
        "Pipe sizing and material selection",
        "Pump selection and installation",
        "Water quality management",
        "Leak detection and repair",
        "System optimization"
      ],
      applications: ["Residential Buildings", "Commercial Complexes", "Industrial Plants", "Hospitals", "Schools"]
    },
    {
      id: 5,
      title: "Solar Power",
      icon: Sun,
      color: "from-yellow-500 to-yellow-600",
      description: "Sustainable solar energy solutions for reduced costs and environmental impact.",
      features: [
        "Solar Panel Installation",
        "Grid-tie Systems",
        "Battery Storage",
        "Energy Monitoring",
        "Net Metering",
        "Maintenance Services"
      ],
      capabilities: [
        "Solar resource assessment",
        "System design and modeling",
        "Financial analysis and ROI",
        "Grid interconnection",
        "Performance monitoring",
        "Maintenance and support"
      ],
      applications: ["Rooftop Solar", "Ground Mount Systems", "Commercial Solar", "Industrial Solar", "Solar Farms"]
    },
    {
      id: 6,
      title: "IBMS (Intelligent Building Management)",
      icon: Building,
      color: "from-purple-500 to-purple-600",
      description: "Intelligent Building Management Systems for automated control and monitoring.",
      features: [
        "Building Automation",
        "Energy Management",
        "Security Integration",
        "Environmental Monitoring",
        "Predictive Maintenance",
        "Data Analytics"
      ],
      capabilities: [
        "System integration and programming",
        "Sensor network design",
        "User interface development",
        "Data collection and analysis",
        "Remote monitoring setup",
        "Performance optimization"
      ],
      applications: ["Smart Buildings", "Corporate Offices", "Hospitals", "Universities", "Manufacturing Facilities"]
    }
  ];

  const toggleService = (serviceId: number) => {
    setExpandedService(expandedService === serviceId ? null : serviceId);
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section ref={heroRef} className="relative py-20 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center space-y-6"
          >
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900">
              Our <span className="text-blue-600">Services</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Comprehensive electrical and MEP engineering solutions tailored to meet your project requirements with precision and excellence.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section ref={servicesRef} className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              const isExpanded = expandedService === service.id;
              
              return (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={servicesInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300"
                >
                  {/* Service Header */}
                  <div 
                    className="p-8 cursor-pointer"
                    onClick={() => toggleService(service.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-6">
                        <div className={`w-16 h-16 rounded-xl bg-gradient-to-br ${service.color} flex items-center justify-center`}>
                          <IconComponent className="w-8 h-8 text-white" />
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">{service.title}</h3>
                          <p className="text-gray-600 mt-2 max-w-2xl">{service.description}</p>
                        </div>
                      </div>
                      <motion.div
                        animate={{ rotate: isExpanded ? 180 : 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <ChevronDown className="w-6 h-6 text-gray-400" />
                      </motion.div>
                    </div>
                  </div>

                  {/* Expanded Content */}
                  <motion.div
                    initial={false}
                    animate={{ height: isExpanded ? "auto" : 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-8 pb-8 border-t border-gray-100">
                      <div className="grid md:grid-cols-3 gap-8 pt-8">
                        {/* Features */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                            Key Features
                          </h4>
                          <ul className="space-y-2">
                            {service.features.map((feature, idx) => (
                              <li key={idx} className="text-gray-600 text-sm flex items-center">
                                <ArrowRight className="w-3 h-3 mr-2 text-gray-400" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Capabilities */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                            <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                            Our Capabilities
                          </h4>
                          <ul className="space-y-2">
                            {service.capabilities.map((capability, idx) => (
                              <li key={idx} className="text-gray-600 text-sm flex items-center">
                                <ArrowRight className="w-3 h-3 mr-2 text-gray-400" />
                                {capability}
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Applications */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                            <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                            Applications
                          </h4>
                          <div className="space-y-2">
                            {service.applications.map((application, idx) => (
                              <div key={idx} className="inline-block bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full mr-2 mb-2">
                                {application}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* CTA */}
                      <div className="mt-8 pt-6 border-t border-gray-100">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Get Quote for {service.title}
                          <ArrowRight className="ml-2 w-4 h-4" />
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-600 to-purple-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={servicesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-white">
              Ready to Start Your Project?
            </h2>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Contact us today to discuss your requirements and get a customized solution for your project.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors"
              >
                Contact Us Today
                <ArrowRight className="ml-2 w-5 h-5" />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-8 py-4 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
              >
                View Our Projects
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ServicesPage;
